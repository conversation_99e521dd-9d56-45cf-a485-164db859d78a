"""
Enhanced NSE Symbol Processor for downloading, parsing, and filtering NSE symbols.
Handles NSE_CM.csv and NSE_FO.csv files with robust error handling, retry mechanisms,
and comprehensive logging of failed rows.
"""

import os
import re
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Set
from pathlib import Path
import psycopg2
from psycopg2.extras import RealDictCursor
import time

from .symbol_downloader import EnhancedSymbolDownloader
from ..database.connection import engine
from ..core.config import settings

logger = logging.getLogger(__name__)


class NSESymbolProcessor:
    """Enhanced NSE symbol processor with robust error handling and retry mechanisms."""

    def __init__(self, config_path: str = None):
        """Initialize the enhanced NSE symbol processor."""
        self.config_path = config_path or "config.yaml"
        self.downloader = EnhancedSymbolDownloader(config_path)
        self.db_engine = engine

        # Create logs directory if it doesn't exist
        self.logs_dir = Path("logs")
        self.logs_dir.mkdir(exist_ok=True)

        # Failed rows tracking
        self.failed_rows = {
            'nse_cm': [],
            'nse_fo': [],
            'symbol_mapping': []
        }

        # Retry configuration
        self.max_retries = 3
        self.retry_delay = 1  # seconds

        # Symbol patterns for filtering based on actual Fyers symbol formats
        # These patterns match the actual symbols found in NSE CSV files
        self.patterns = {
            'EQUITY': re.compile(r'^[A-Z0-9&\-]+\-EQ$'),
            'INDEX': re.compile(r'^[A-Z0-9\-]+\-INDEX$'),
            'FUTURES': re.compile(r'^[A-Z0-9&\-]+\d{2}[A-Z]{3}FUT$'),
            'OPTIONS_MONTHLY': re.compile(r'^[A-Z0-9&\-]+\d{2}[A-Z]{3}\d+(?:\.\d+)?(?:CE|PE)$'),
            'OPTIONS_WEEKLY': re.compile(r'^[A-Z0-9&\-]+\d{2}\d{3}\d+(?:\.\d+)?(?:CE|PE)$')
        }
        
        # NSE_CM.csv column mapping (no headers in file)
        self.nse_cm_columns = [
            'fytoken', 'company_name', 'segment', 'lot_size', 'tick_size',
            'isin', 'trading_hours', 'last_update_date', 'expiry_timestamp',
            'symbol_name', 'exchange_segment', 'exchange_instrument_id',
            'instrument_id', 'symbol', 'token', 'minimum_lot_size',
            'instrument_type', 'underlying_fytoken', 'underlying_symbol',
            'option_type', 'strike_price'
        ]
        
        # NSE_FO.csv column mapping (no headers in file)
        self.nse_fo_columns = [
            'fytoken', 'company_name', 'segment', 'lot_size', 'tick_size',
            'isin', 'trading_hours', 'last_update_date', 'expiry_timestamp',
            'symbol_name', 'exchange_segment', 'exchange_instrument_id',
            'instrument_id', 'symbol', 'token', 'minimum_lot_size',
            'instrument_type', 'underlying_fytoken', 'underlying_symbol',
            'option_type', 'strike_price'
        ]
    
    def download_daily_symbols(self) -> Dict[str, bool]:
        """Download NSE_CM.csv and NSE_FO.csv with backup."""
        logger.info("Starting daily symbol download")
        return self.downloader.download_daily_symbols()
    
    def _parse_csv_file(self, file_path: Path, columns: List[str]) -> pd.DataFrame:
        """Parse CSV file without headers."""
        try:
            df = pd.read_csv(file_path, header=None, names=columns)
            logger.info(f"Parsed {len(df)} rows from {file_path}")
            return df
        except Exception as e:
            logger.error(f"Error parsing {file_path}: {e}")
            return pd.DataFrame()
    
    def _create_tables_if_not_exist(self) -> bool:
        """Create NSE raw data tables if they don't exist."""
        try:
            # Use psycopg2 for raw SQL execution
            db_url = settings.database.url
            conn = psycopg2.connect(db_url)

            try:
                with conn.cursor() as cursor:
                    # Read and execute schema
                    schema_path = Path("src/database/schemas.sql")
                    if schema_path.exists():
                        with open(schema_path, 'r') as f:
                            schema_sql = f.read()

                        # Execute the entire schema as one block to handle PL/pgSQL
                        try:
                            cursor.execute(schema_sql)
                        except Exception as schema_error:
                            # If schema execution fails, rollback and try to create just the NSE tables
                            logger.warning(f"Full schema execution failed: {schema_error}")
                            logger.info("Attempting to create NSE tables only...")

                            # Rollback the failed transaction
                            conn.rollback()

                            # Start a new transaction and create just the NSE raw tables
                            with conn.cursor() as new_cursor:
                                # Create just the NSE raw tables
                                nse_tables_sql = """
                                -- Create NSE_CM Raw Data Table (Cash Market - Equities and Indices)
                                CREATE TABLE IF NOT EXISTS nse_cm_raw (
                                    id SERIAL PRIMARY KEY,
                                    fytoken VARCHAR(50) NOT NULL,
                                    company_name VARCHAR(200),
                                    segment INTEGER,
                                    lot_size INTEGER,
                                    tick_size DECIMAL(10,4),
                                    isin VARCHAR(20),
                                    trading_hours VARCHAR(50),
                                    last_update_date DATE,
                                    expiry_timestamp BIGINT,
                                    symbol_name VARCHAR(100),
                                    exchange_segment INTEGER,
                                    exchange_instrument_id INTEGER,
                                    instrument_id INTEGER,
                                    symbol VARCHAR(100),
                                    token INTEGER,
                                    minimum_lot_size DECIMAL(12,4),
                                    instrument_type VARCHAR(10),
                                    underlying_fytoken VARCHAR(50),
                                    underlying_symbol VARCHAR(100),
                                    option_type INTEGER,
                                    strike_price DECIMAL(12,4),
                                    created_at TIMESTAMPTZ DEFAULT NOW(),
                                    updated_at TIMESTAMPTZ DEFAULT NOW()
                                );

                                -- Create NSE_FO Raw Data Table (Futures and Options)
                                CREATE TABLE IF NOT EXISTS nse_fo_raw (
                                    id SERIAL PRIMARY KEY,
                                    fytoken VARCHAR(50) NOT NULL,
                                    company_name VARCHAR(200),
                                    segment INTEGER,
                                    lot_size INTEGER,
                                    tick_size DECIMAL(10,4),
                                    isin VARCHAR(20),
                                    trading_hours VARCHAR(50),
                                    last_update_date DATE,
                                    expiry_timestamp BIGINT,
                                    symbol_name VARCHAR(100),
                                    exchange_segment INTEGER,
                                    exchange_instrument_id INTEGER,
                                    instrument_id INTEGER,
                                    symbol VARCHAR(100),
                                    token INTEGER,
                                    minimum_lot_size DECIMAL(12,4),
                                    instrument_type VARCHAR(10),
                                    underlying_fytoken VARCHAR(50),
                                    underlying_symbol VARCHAR(100),
                                    option_type VARCHAR(10),
                                    strike_price DECIMAL(12,4),
                                    created_at TIMESTAMPTZ DEFAULT NOW(),
                                    updated_at TIMESTAMPTZ DEFAULT NOW()
                                );
                                """
                                new_cursor.execute(nse_tables_sql)

                        conn.commit()
                        logger.info("Database tables created/verified successfully")
                        return True
                    else:
                        logger.error("Schema file not found")
                        return False
            finally:
                conn.close()

        except Exception as e:
            logger.error(f"Error creating tables: {e}")
            return False
    
    def _load_data_to_raw_table(self, df: pd.DataFrame, table_name: str) -> bool:
        """Load DataFrame to raw NSE table with error handling and retry mechanism."""
        if df.empty:
            logger.warning(f"No data to load into {table_name}")
            return False

        successful_rows = 0
        failed_rows = []

        for attempt in range(self.max_retries):
            try:
                # Use psycopg2 for raw SQL execution
                db_url = settings.database.url
                conn = psycopg2.connect(db_url)

                try:
                    with conn.cursor() as cursor:
                        # Clear existing data only on first attempt
                        if attempt == 0:
                            cursor.execute(f"DELETE FROM {table_name}")
                            logger.info(f"Cleared existing data from {table_name}")

                        # Prepare insert statement
                        columns = list(df.columns)
                        placeholders = ', '.join(['%s'] * len(columns))
                        insert_sql = f"""
                            INSERT INTO {table_name} ({', '.join(columns)})
                            VALUES ({placeholders})
                        """

                        # Insert data row by row with error handling using individual transactions
                        for idx, row in df.iterrows():
                            try:
                                # Handle NaN/NaT values and clean data
                                row_values = []
                                for col_name, val in row.items():
                                    if pd.isna(val) or (isinstance(val, str) and val.strip().lower() in ['nan', 'nat', '']):
                                        row_values.append(None)
                                    elif isinstance(val, str) and val.strip() == '':
                                        row_values.append(None)
                                    elif col_name == 'expiry_timestamp' and isinstance(val, str) and val.strip() == ' ':
                                        row_values.append(None)  # Handle space character in expiry_timestamp
                                    else:
                                        row_values.append(val)

                                # Use individual transaction for each row
                                cursor.execute(insert_sql, tuple(row_values))
                                conn.commit()  # Commit each row individually
                                successful_rows += 1

                            except Exception as row_error:
                                # Rollback only this row's transaction
                                conn.rollback()

                                error_info = {
                                    'row_index': idx,
                                    'error': str(row_error),
                                    'data': row.to_dict(),
                                    'attempt': attempt + 1
                                }
                                failed_rows.append(error_info)
                                # Don't log every single failure to avoid spam
                                if idx % 1000 == 0:  # Log every 1000th failure
                                    logger.warning(f"Failed to insert row {idx} in {table_name}: {row_error}")

                        # Final commit (though individual rows are already committed)
                        conn.commit()
                        logger.info(f"Attempt {attempt + 1}: Loaded {successful_rows} rows into {table_name}")

                        # Store failed rows for this table
                        if table_name == 'nse_cm_raw':
                            self.failed_rows['nse_cm'].extend(failed_rows)
                        elif table_name == 'nse_fo_raw':
                            self.failed_rows['nse_fo'].extend(failed_rows)

                        # If we have some successful rows, consider it a partial success
                        if successful_rows > 0:
                            self._write_failed_rows_to_file(table_name, failed_rows)
                            return True

                finally:
                    conn.close()

            except Exception as e:
                logger.error(f"Attempt {attempt + 1} failed for {table_name}: {e}")
                if attempt < self.max_retries - 1:
                    logger.info(f"Retrying in {self.retry_delay} seconds...")
                    time.sleep(self.retry_delay)
                else:
                    logger.error(f"All {self.max_retries} attempts failed for {table_name}")

        # Write failed rows to file
        self._write_failed_rows_to_file(table_name, failed_rows)
        return successful_rows > 0

    def _load_data_to_raw_table_incremental(self, df: pd.DataFrame, table_name: str) -> bool:
        """Load DataFrame to raw NSE table with incremental updates for better performance."""
        if df.empty:
            logger.warning(f"No data to load into {table_name}")
            return False

        successful_rows = 0
        failed_rows = []

        for attempt in range(self.max_retries):
            try:
                # Use psycopg2 for raw SQL execution
                db_url = settings.database.url
                conn = psycopg2.connect(db_url)

                try:
                    with conn.cursor() as cursor:
                        # Get existing symbols to identify new ones
                        if attempt == 0:
                            cursor.execute(f"SELECT DISTINCT symbol_name FROM {table_name} WHERE DATE(created_at) = CURRENT_DATE")
                            existing_symbols = {row[0] for row in cursor.fetchall()}
                            logger.info(f"Found {len(existing_symbols)} existing symbols in {table_name} for today")
                        else:
                            existing_symbols = set()

                        # Filter new symbols only
                        new_rows = []
                        for _, row in df.iterrows():
                            symbol_name = row.get('symbol_name', '')
                            if symbol_name not in existing_symbols:
                                new_rows.append(row)

                        if not new_rows:
                            logger.info(f"No new symbols to add to {table_name}")
                            return True

                        logger.info(f"Adding {len(new_rows)} new symbols to {table_name}")

                        # Prepare insert statement (no ON CONFLICT since there's no unique constraint)
                        columns = list(df.columns)
                        placeholders = ', '.join(['%s'] * len(columns))
                        insert_sql = f"""
                            INSERT INTO {table_name} ({', '.join(columns)})
                            VALUES ({placeholders})
                        """

                        # Insert new data only with individual transactions
                        for idx, row in enumerate(new_rows):
                            try:
                                # Handle NaN/NaT values and clean data
                                row_values = []
                                for col_name, val in row.items():
                                    if pd.isna(val) or (isinstance(val, str) and val.strip().lower() in ['nan', 'nat', '']):
                                        row_values.append(None)
                                    elif isinstance(val, str) and val.strip() == '':
                                        row_values.append(None)
                                    elif col_name == 'expiry_timestamp' and isinstance(val, str) and val.strip() == ' ':
                                        row_values.append(None)
                                    else:
                                        row_values.append(val)

                                # Use individual transaction for each row
                                cursor.execute(insert_sql, tuple(row_values))
                                conn.commit()  # Commit each row individually
                                successful_rows += 1

                            except Exception as row_error:
                                # Rollback only this row's transaction
                                conn.rollback()

                                error_info = {
                                    'row_index': idx,
                                    'error': str(row_error),
                                    'data': row.to_dict(),
                                    'attempt': attempt + 1
                                }
                                failed_rows.append(error_info)
                                # Don't log every single failure to avoid spam
                                if idx % 1000 == 0:  # Log every 1000th failure
                                    logger.warning(f"Failed to insert row {idx} in {table_name}: {row_error}")

                        conn.commit()
                        logger.info(f"Attempt {attempt + 1}: Loaded {successful_rows} new rows into {table_name}")

                        # Store failed rows for this table
                        if table_name == 'nse_cm_raw':
                            self.failed_rows['nse_cm'].extend(failed_rows)
                        elif table_name == 'nse_fo_raw':
                            self.failed_rows['nse_fo'].extend(failed_rows)

                        if successful_rows > 0 or len(new_rows) == 0:
                            self._write_failed_rows_to_file(table_name, failed_rows)
                            return True

                finally:
                    conn.close()

            except Exception as e:
                logger.error(f"Attempt {attempt + 1} failed for {table_name}: {e}")
                if attempt < self.max_retries - 1:
                    logger.info(f"Retrying in {self.retry_delay} seconds...")
                    time.sleep(self.retry_delay)
                else:
                    logger.error(f"All {self.max_retries} attempts failed for {table_name}")

        # Write failed rows to file
        self._write_failed_rows_to_file(table_name, failed_rows)
        return successful_rows > 0
    
    def _extract_symbol_info(self, symbol: str) -> Dict[str, any]:
        """Extract market type and other info from symbol using actual Fyers symbol formats."""
        symbol_info = {
            'market_type': None,
            'underlying': None,
            'expiry_date': None,
            'strike_price': None,
            'option_type': None
        }

        # Check EQUITY pattern: UNDERLYING-EQ (e.g., RELIANCE-EQ)
        if self.patterns['EQUITY'].match(symbol):
            symbol_info['market_type'] = 'EQUITY'
            symbol_info['underlying'] = symbol.replace('-EQ', '')

        # Check INDEX pattern: UNDERLYING-INDEX (e.g., NIFTY50-INDEX)
        elif self.patterns['INDEX'].match(symbol):
            symbol_info['market_type'] = 'INDEX'
            symbol_info['underlying'] = symbol.replace('-INDEX', '')

        # Check FUTURES pattern: UNDERLYINGYYMMMFUT (e.g., RELIANCE25JULFUT, M&M25JULFUT, BAJAJ-AUTO25JULFUT)
        elif self.patterns['FUTURES'].match(symbol):
            symbol_info['market_type'] = 'FUTURES'
            # Extract underlying and expiry from UNDERLYINGYYMMMFUT (handle &, - characters)
            match = re.match(r'^([A-Z0-9&\-]+)(\d{2})([A-Z]{3})FUT$', symbol)
            if match:
                symbol_info['underlying'] = match.group(1)
                year = int(f"20{match.group(2)}")
                month_str = match.group(3)
                symbol_info['expiry_date'] = self._parse_expiry_date(year, month_str)

        # Check OPTIONS monthly pattern: UNDERLYINGYYMMMSTRIKECE/PE (e.g., RELIANCE25JUL2500CE, M&M25JUL2500CE, BAJAJ-AUTO25JUL5900CE)
        elif self.patterns['OPTIONS_MONTHLY'].match(symbol):
            symbol_info['market_type'] = 'OPTIONS'
            # Extract from UNDERLYINGYYMMMSTRIKECE/PE (handle &, - characters)
            match = re.match(r'^([A-Z0-9&\-]+)(\d{2})([A-Z]{3})(\d+(?:\.\d+)?)(CE|PE)$', symbol)
            if match:
                symbol_info['underlying'] = match.group(1)
                year = int(f"20{match.group(2)}")
                month_str = match.group(3)
                symbol_info['expiry_date'] = self._parse_expiry_date(year, month_str)
                symbol_info['strike_price'] = float(match.group(4))
                symbol_info['option_type'] = match.group(5)

        # Check OPTIONS weekly pattern: UNDERLYINGYYMDDSTRIKECE/PE (e.g., NIFTY2572425050CE)
        # Format: UNDERLYING + YY + MDD + STRIKE + CE/PE where MDD is month(1 digit) + day(2 digits)
        elif self.patterns['OPTIONS_WEEKLY'].match(symbol):
            symbol_info['market_type'] = 'OPTIONS'
            # Extract from UNDERLYINGYYMDDSTRIKECE/PE (e.g., NIFTY2572425050CE, handle &, - characters)
            match = re.match(r'^([A-Z0-9&\-]+)(\d{2})(\d{3})(\d+(?:\.\d+)?)(CE|PE)$', symbol)
            if match:
                symbol_info['underlying'] = match.group(1)
                year = int(f"20{match.group(2)}")
                mdd = match.group(3)  # month(1 digit) + day(2 digits)
                month_digit = mdd[0]  # First digit is month
                day = int(mdd[1:3])   # Next two digits are day
                symbol_info['expiry_date'] = self._parse_weekly_expiry_date(year, month_digit, day)
                symbol_info['strike_price'] = float(match.group(4))
                symbol_info['option_type'] = match.group(5)

        return symbol_info
    
    def _parse_expiry_date(self, year: int, month_str: str) -> Optional[datetime]:
        """Parse expiry date from year and month string."""
        month_map = {
            'JAN': 1, 'FEB': 2, 'MAR': 3, 'APR': 4, 'MAY': 5, 'JUN': 6,
            'JUL': 7, 'AUG': 8, 'SEP': 9, 'OCT': 10, 'NOV': 11, 'DEC': 12
        }
        
        try:
            month = month_map.get(month_str)
            if month:
                # Last Thursday of the month (typical expiry)
                last_day = datetime(year, month + 1, 1) - timedelta(days=1) if month < 12 else datetime(year, 12, 31)
                while last_day.weekday() != 3:  # Thursday is 3
                    last_day -= timedelta(days=1)
                return last_day
        except Exception as e:
            logger.warning(f"Error parsing expiry date {year}-{month_str}: {e}")
        
        return None
    
    def _parse_weekly_expiry_date(self, year: int, month_digit: str, day: int) -> Optional[datetime]:
        """Parse weekly expiry date from year, month digit, and day with validation."""
        # Month digit mapping (1-9, O, N, D for Oct, Nov, Dec)
        month_map = {
            '1': 1, '2': 2, '3': 3, '4': 4, '5': 5, '6': 6,
            '7': 7, '8': 8, '9': 9, 'O': 10, 'N': 11, 'D': 12
        }

        try:
            month = month_map.get(month_digit)
            if month and 1 <= day <= 31:
                # Validate the date is actually valid for the month
                try:
                    return datetime(year, month, day)
                except ValueError:
                    # Invalid date (e.g., Feb 30), skip it
                    return None
        except Exception as e:
            logger.debug(f"Error parsing weekly expiry date {year}-{month_digit}-{day}: {e}")

        return None

    def _write_failed_rows_to_file(self, table_name: str, failed_rows: List[Dict]) -> None:
        """Write failed rows to a text file in the logs directory."""
        if not failed_rows:
            return

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = self.logs_dir / f"{table_name}_failed_rows_{timestamp}.txt"

        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(f"Failed rows for {table_name} - {datetime.now()}\n")
                f.write("=" * 80 + "\n\n")

                for i, failed_row in enumerate(failed_rows, 1):
                    f.write(f"Failed Row #{i}:\n")
                    f.write(f"  Row Index: {failed_row['row_index']}\n")
                    f.write(f"  Attempt: {failed_row['attempt']}\n")
                    f.write(f"  Error: {failed_row['error']}\n")
                    f.write(f"  Data: {failed_row['data']}\n")
                    f.write("-" * 40 + "\n")

                f.write(f"\nTotal failed rows: {len(failed_rows)}\n")

            logger.info(f"Failed rows written to: {filename}")

        except Exception as e:
            logger.error(f"Error writing failed rows to file: {e}")

    def _write_skipped_symbols(self, skipped_symbols: List[str], market_segment: str) -> None:
        """Write skipped symbols to a text file for analysis."""
        if not skipped_symbols:
            return

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = self.logs_dir / f"skipped_symbols_{market_segment}_{timestamp}.txt"

        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(f"Skipped symbols for {market_segment} - {datetime.now()}\n")
                f.write("=" * 80 + "\n\n")
                f.write(f"Total skipped symbols: {len(skipped_symbols)}\n\n")

                for i, symbol in enumerate(skipped_symbols, 1):
                    f.write(f"{i:4d}. {symbol}\n")

            logger.info(f"Skipped symbols written to: {filename}")

        except Exception as e:
            logger.error(f"Error writing skipped symbols to file: {e}")

    def _get_symbols_not_loaded(self) -> Dict[str, Set[str]]:
        """Get list of symbols that were not loaded successfully."""
        not_loaded = {
            'EQUITY': set(),
            'INDEX': set(),
            'FUTURES': set(),
            'OPTIONS': set()
        }

        # Extract symbols from failed rows
        for failed_row in self.failed_rows['nse_cm'] + self.failed_rows['nse_fo']:
            try:
                symbol_name = failed_row['data'].get('symbol_name', '')
                if ':' in symbol_name:
                    symbol = symbol_name.split(':')[-1]
                else:
                    symbol = symbol_name

                symbol_info = self._extract_symbol_info(symbol)
                market_type = symbol_info.get('market_type')

                if market_type and market_type in not_loaded:
                    not_loaded[market_type].add(symbol)

            except Exception as e:
                logger.debug(f"Error extracting symbol from failed row: {e}")

        return not_loaded

    def _filter_relevant_symbols(self, df: pd.DataFrame) -> pd.DataFrame:
        """Filter symbols based on pattern matching criteria and track skipped symbols."""
        if df.empty:
            return df

        filtered_rows = []
        skipped_symbols = []
        symbol_counts = {
            'EQUITY': 0,
            'INDEX': 0,
            'FUTURES': 0,
            'OPTIONS': 0,
            'SKIPPED': 0
        }

        for _, row in df.iterrows():
            # Extract NSE symbol from Fyers symbol format
            fyers_symbol = str(row.get('symbol_name', '')).strip()
            if fyers_symbol.startswith('NSE:'):
                nse_symbol = fyers_symbol[4:]  # Remove 'NSE:' prefix
            else:
                skipped_symbols.append(fyers_symbol)
                symbol_counts['SKIPPED'] += 1
                continue

            symbol_info = self._extract_symbol_info(nse_symbol)

            if symbol_info['market_type']:
                # Add symbol info to row
                row_dict = row.to_dict()
                row_dict.update(symbol_info)
                row_dict['nse_symbol'] = nse_symbol  # Store NSE symbol separately
                filtered_rows.append(row_dict)
                symbol_counts[symbol_info['market_type']] += 1
            else:
                skipped_symbols.append(nse_symbol)
                symbol_counts['SKIPPED'] += 1

        # Log symbol counts
        logger.info(f"Symbol filtering results: {symbol_counts}")

        # Write skipped symbols to file for analysis
        if skipped_symbols:
            market_segment = "CM" if "NSE_CM" in str(df.get('source_file', '')) else "FO"
            self._write_skipped_symbols(skipped_symbols, market_segment)

        if filtered_rows:
            filtered_df = pd.DataFrame(filtered_rows)
            logger.info(f"Filtered {len(filtered_df)} relevant symbols from {len(df)} total symbols")
            return filtered_df
        else:
            logger.warning("No relevant symbols found after filtering")
            return pd.DataFrame()

    def _update_symbol_mapping_table(self, df: pd.DataFrame) -> bool:
        """Update symbol_mapping table with filtered symbols and error handling."""
        if df.empty:
            logger.warning("No data to update in symbol_mapping table")
            return False

        successful_inserts = 0
        failed_inserts = []

        for attempt in range(self.max_retries):
            try:
                # Use psycopg2 for raw SQL execution
                db_url = settings.database.url
                conn = psycopg2.connect(db_url)

                try:
                    with conn.cursor() as cursor:
                        # Clear existing data for today only on first attempt
                        if attempt == 0:
                            cursor.execute("DELETE FROM symbol_mapping WHERE DATE(created_at) = CURRENT_DATE")
                            logger.info("Cleared existing symbol_mapping data for today")

                        # Insert new symbols
                        insert_sql = """
                            INSERT INTO symbol_mapping (
                                nse_symbol, fyers_symbol, market_type, exchange,
                                expiry_date, strike_price, option_type, is_active
                            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                        """

                        for idx, row in df.iterrows():
                            try:
                                # Use the pre-extracted NSE symbol and original Fyers symbol
                                nse_symbol = row.get('nse_symbol', '')
                                fyers_symbol = row.get('symbol_name', '')
                                market_type = row.get('market_type')
                                expiry_date = row.get('expiry_date')
                                strike_price = row.get('strike_price')
                                option_type = row.get('option_type')

                                # Handle NaN/NaT values properly
                                if pd.isna(expiry_date) or (isinstance(expiry_date, str) and expiry_date.lower() in ['nan', 'nat']):
                                    expiry_date = None
                                if pd.isna(strike_price) or (isinstance(strike_price, str) and strike_price.lower() in ['nan', 'nat']):
                                    strike_price = None
                                if pd.isna(option_type) or (isinstance(option_type, str) and option_type.lower() in ['nan', 'nat']):
                                    option_type = None

                                cursor.execute(insert_sql, (
                                    nse_symbol, fyers_symbol, market_type, 'NSE',
                                    expiry_date, strike_price, option_type, True
                                ))
                                successful_inserts += 1

                            except Exception as row_error:
                                error_info = {
                                    'row_index': idx,
                                    'error': str(row_error),
                                    'data': row.to_dict(),
                                    'attempt': attempt + 1
                                }
                                failed_inserts.append(error_info)
                                logger.warning(f"Failed to insert symbol mapping row {idx}: {row_error}")

                        conn.commit()
                        logger.info(f"Attempt {attempt + 1}: Updated symbol_mapping table with {successful_inserts} symbols")

                        # Store failed rows
                        self.failed_rows['symbol_mapping'].extend(failed_inserts)

                        if successful_inserts > 0:
                            self._write_failed_rows_to_file('symbol_mapping', failed_inserts)
                            return True

                finally:
                    conn.close()

            except Exception as e:
                logger.error(f"Attempt {attempt + 1} failed for symbol_mapping: {e}")
                if attempt < self.max_retries - 1:
                    logger.info(f"Retrying in {self.retry_delay} seconds...")
                    time.sleep(self.retry_delay)
                else:
                    logger.error(f"All {self.max_retries} attempts failed for symbol_mapping")

        # Write failed rows to file
        self._write_failed_rows_to_file('symbol_mapping', failed_inserts)
        return successful_inserts > 0

    def process_nse_files(self) -> Dict[str, bool]:
        """Main method to process NSE files - download, parse, filter, and update DB."""
        results = {
            'download': False,
            'nse_cm_processed': False,
            'nse_fo_processed': False,
            'symbol_mapping_updated': False,
            'tables_created': False
        }

        try:
            # Step 1: Create tables if needed
            results['tables_created'] = self._create_tables_if_not_exist()
            if not results['tables_created']:
                logger.error("Failed to create/verify database tables")
                return results

            # Step 2: Download files
            download_results = self.download_daily_symbols()
            results['download'] = all(download_results.values())

            if not results['download']:
                logger.error("Failed to download NSE files")
                return results

            # Step 3: Process NSE_CM.csv with incremental loading
            nse_cm_path = Path("NSE_CM.csv")
            if nse_cm_path.exists():
                nse_cm_df = self._parse_csv_file(nse_cm_path, self.nse_cm_columns)
                if not nse_cm_df.empty:
                    nse_cm_df['source_file'] = 'NSE_CM'  # Add source file identifier
                    results['nse_cm_processed'] = self._load_data_to_raw_table_incremental(nse_cm_df, 'nse_cm_raw')

            # Step 4: Process NSE_FO.csv with incremental loading
            nse_fo_path = Path("NSE_FO.csv")
            if nse_fo_path.exists():
                nse_fo_df = self._parse_csv_file(nse_fo_path, self.nse_fo_columns)
                if not nse_fo_df.empty:
                    nse_fo_df['source_file'] = 'NSE_FO'  # Add source file identifier
                    results['nse_fo_processed'] = self._load_data_to_raw_table_incremental(nse_fo_df, 'nse_fo_raw')

            # Step 5: Filter and update symbol mapping
            all_symbols = []

            if results['nse_cm_processed'] and not nse_cm_df.empty:
                filtered_cm = self._filter_relevant_symbols(nse_cm_df)
                if not filtered_cm.empty:
                    all_symbols.append(filtered_cm)

            if results['nse_fo_processed'] and not nse_fo_df.empty:
                filtered_fo = self._filter_relevant_symbols(nse_fo_df)
                if not filtered_fo.empty:
                    all_symbols.append(filtered_fo)

            if all_symbols:
                combined_symbols = pd.concat(all_symbols, ignore_index=True)
                results['symbol_mapping_updated'] = self._update_symbol_mapping_table(combined_symbols)

            # Step 6: Generate summary report
            self._generate_processing_summary(results)

            # Step 7: Cleanup old backups
            self.downloader.cleanup_old_backups()

            logger.info(f"NSE processing completed. Results: {results}")
            return results

        except Exception as e:
            logger.error(f"Error in process_nse_files: {e}")
            return results

    def _generate_processing_summary(self, results: Dict[str, bool]) -> None:
        """Generate a comprehensive processing summary."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        summary_file = self.logs_dir / f"nse_processing_summary_{timestamp}.txt"

        try:
            with open(summary_file, 'w', encoding='utf-8') as f:
                f.write(f"NSE Symbol Processing Summary - {datetime.now()}\n")
                f.write("=" * 80 + "\n\n")

                # Processing results
                f.write("Processing Results:\n")
                for key, value in results.items():
                    status = "✅ SUCCESS" if value else "❌ FAILED"
                    f.write(f"  {key}: {status}\n")
                f.write("\n")

                # Failed rows summary
                f.write("Failed Rows Summary:\n")
                total_failed = 0
                for table, failed_list in self.failed_rows.items():
                    count = len(failed_list)
                    total_failed += count
                    f.write(f"  {table}: {count} failed rows\n")
                f.write(f"  Total failed rows: {total_failed}\n\n")

                # Symbols not loaded
                not_loaded = self._get_symbols_not_loaded()
                f.write("Symbols Not Loaded:\n")
                for market_type, symbols in not_loaded.items():
                    f.write(f"  {market_type}: {len(symbols)} symbols\n")
                    if symbols:
                        # Show first 10 symbols as examples
                        examples = list(symbols)[:10]
                        f.write(f"    Examples: {', '.join(examples)}\n")
                        if len(symbols) > 10:
                            f.write(f"    ... and {len(symbols) - 10} more\n")
                f.write("\n")

            logger.info(f"Processing summary written to: {summary_file}")

        except Exception as e:
            logger.error(f"Error writing processing summary: {e}")

    def get_sample_symbols_by_type(self) -> Dict[str, str]:
        """Get one sample symbol from each market type for testing."""
        try:
            # Use psycopg2 for raw SQL execution
            db_url = settings.database.url
            conn = psycopg2.connect(db_url)

            try:
                with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                    sample_symbols = {}

                    for market_type in ['EQUITY', 'INDEX', 'FUTURES', 'OPTIONS']:
                        cursor.execute("""
                            SELECT nse_symbol, fyers_symbol
                            FROM symbol_mapping
                            WHERE market_type = %s
                            AND is_active = TRUE
                            LIMIT 1
                        """, (market_type,))

                        result = cursor.fetchone()
                        if result:
                            sample_symbols[market_type] = result['fyers_symbol']

                    return sample_symbols
            finally:
                conn.close()

        except Exception as e:
            logger.error(f"Error getting sample symbols: {e}")
            return {}

    def fix_null_fyers_symbols(self) -> Dict[str, int]:
        """Fix null fyers_symbol values in market type tables by updating from symbol_mapping."""
        try:
            db_url = settings.database.url
            conn = psycopg2.connect(db_url)

            update_counts = {
                'equity_ohlcv': 0,
                'index_ohlcv': 0,
                'futures_ohlcv': 0,
                'options_ohlcv': 0
            }

            try:
                with conn.cursor() as cursor:
                    # Update equity_ohlcv table
                    cursor.execute("""
                        UPDATE equity_ohlcv
                        SET fyers_symbol = sm.fyers_symbol
                        FROM symbol_mapping sm
                        WHERE equity_ohlcv.symbol = sm.nse_symbol
                        AND sm.market_type = 'EQUITY'
                        AND equity_ohlcv.fyers_symbol IS NULL
                    """)
                    update_counts['equity_ohlcv'] = cursor.rowcount

                    # Update index_ohlcv table
                    cursor.execute("""
                        UPDATE index_ohlcv
                        SET fyers_symbol = sm.fyers_symbol
                        FROM symbol_mapping sm
                        WHERE index_ohlcv.symbol = sm.nse_symbol
                        AND sm.market_type = 'INDEX'
                        AND index_ohlcv.fyers_symbol IS NULL
                    """)
                    update_counts['index_ohlcv'] = cursor.rowcount

                    # Update futures_ohlcv table
                    cursor.execute("""
                        UPDATE futures_ohlcv
                        SET fyers_symbol = sm.fyers_symbol
                        FROM symbol_mapping sm
                        WHERE futures_ohlcv.symbol = sm.nse_symbol
                        AND sm.market_type = 'FUTURES'
                        AND futures_ohlcv.fyers_symbol IS NULL
                    """)
                    update_counts['futures_ohlcv'] = cursor.rowcount

                    # Update options_ohlcv table
                    cursor.execute("""
                        UPDATE options_ohlcv
                        SET fyers_symbol = sm.fyers_symbol
                        FROM symbol_mapping sm
                        WHERE options_ohlcv.symbol = sm.nse_symbol
                        AND sm.market_type = 'OPTIONS'
                        AND options_ohlcv.fyers_symbol IS NULL
                    """)
                    update_counts['options_ohlcv'] = cursor.rowcount

                    conn.commit()
                    logger.info(f"Fixed null fyers_symbol values: {update_counts}")

            finally:
                conn.close()

            return update_counts

        except Exception as e:
            logger.error(f"Error fixing null fyers_symbols: {e}")
            return {}

    def validate_data_integrity(self) -> Dict[str, any]:
        """Validate data integrity across NSE tables."""
        integrity_report = {
            'nse_cm_count': 0,
            'nse_fo_count': 0,
            'symbol_mapping_count': 0,
            'equity_count': 0,
            'index_count': 0,
            'futures_count': 0,
            'options_count': 0,
            'missing_data': [],
            'validation_passed': False
        }

        try:
            # Use psycopg2 for raw SQL execution
            db_url = settings.database.url
            conn = psycopg2.connect(db_url)

            try:
                with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                    # Count records in each table
                    cursor.execute("SELECT COUNT(*) as count FROM nse_cm_raw")
                    integrity_report['nse_cm_count'] = cursor.fetchone()['count']

                    cursor.execute("SELECT COUNT(*) as count FROM nse_fo_raw")
                    integrity_report['nse_fo_count'] = cursor.fetchone()['count']

                    cursor.execute("SELECT COUNT(*) as count FROM symbol_mapping")
                    integrity_report['symbol_mapping_count'] = cursor.fetchone()['count']

                    # Count by market type
                    for market_type in ['EQUITY', 'INDEX', 'FUTURES', 'OPTIONS']:
                        cursor.execute("""
                            SELECT COUNT(*) as count
                            FROM symbol_mapping
                            WHERE market_type = %s AND is_active = TRUE
                        """, (market_type,))

                        count = cursor.fetchone()['count']
                        integrity_report[f'{market_type.lower()}_count'] = count

                        if count == 0:
                            integrity_report['missing_data'].append(f"No {market_type} symbols found")

                    # Basic validation
                    integrity_report['validation_passed'] = (
                        integrity_report['nse_cm_count'] > 0 and
                        integrity_report['nse_fo_count'] > 0 and
                        integrity_report['symbol_mapping_count'] > 0 and
                        len(integrity_report['missing_data']) == 0
                    )

                    logger.info(f"Data integrity validation: {integrity_report}")
                    return integrity_report
            finally:
                conn.close()

        except Exception as e:
            logger.error(f"Error validating data integrity: {e}")
            integrity_report['missing_data'].append(f"Database error: {e}")
            return integrity_report
